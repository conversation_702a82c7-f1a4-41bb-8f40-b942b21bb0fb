package com.rewardoor.app.services

import discord4j.core.GatewayDiscordClient
import discord4j.core.event.domain.VoiceStateUpdateEvent
import org.springframework.stereotype.Component
import reactor.core.publisher.Mono
import reactor.core.scheduler.Schedulers
import org.springframework.core.env.Environment

@Component
class GatewayDiscordService(
    private val discordGateway: GatewayDiscordClient,
    private val discordService: DiscordService,
    private val env: Environment
) {
    init {
        if (env.matchesProfiles("prod")) {
            discordGateway.on(VoiceStateUpdateEvent::class.java,
                this::handleEvent).subscribe()
        }
    }

    fun handleEvent(event: VoiceStateUpdateEvent): Mono<Void> {
        return Mono.just(event)
            .publishOn(Schedulers.newBoundedElastic(20, 10000, "discord-voice-state"))
            .doOnNext {
                discordService.handleVoiceStateUpdate(it)
            }.then()
    }
}