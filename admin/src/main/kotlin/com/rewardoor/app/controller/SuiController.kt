package com.rewardoor.app.controller

import com.fasterxml.jackson.annotation.JsonSetter
import com.fasterxml.jackson.annotation.Nulls
import com.rewardoor.IdGenerator
import com.rewardoor.app.auth.Jwt
import com.rewardoor.app.dao.ParticipantRepository
import com.rewardoor.app.dao.SuiSbtRepository
import com.rewardoor.app.dao.TonSocietySyncRepository
import com.rewardoor.app.dto.SocialAccountBound
import com.rewardoor.app.dto.UserSuiDto
import com.rewardoor.app.services.*
import com.rewardoor.model.*
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import org.springframework.web.server.ResponseStatusException
import org.ton.java.mnemonic.Ed25519
import java.time.Instant
import java.util.HexFormat

@RestController
@RequestMapping("/sui")
class SuiController(
    private val userService: UserService,
    private val nonceService: NonceService,
    private val suiService: SuiService,
    private val campaignService: CampaignService,
    @Value("\${airdrop.ton_secret_key}") private val secretKey: String,
    private val wiseScoreService: WiseScoreService,
    private val credentialGroupService: CredentialGroupService,
    private val credentialService: CredentialService,
    private val idGenerator: IdGenerator,
    private val suiSbtRepository: SuiSbtRepository,
    private val tonSyncService: TonSyncService,
    private val tonSocietySyncRepository: TonSocietySyncRepository,
    private val participantRepository: ParticipantRepository
) {
    @GetMapping("nonce")
    @Operation(summary = "Get nonce for Sui wallet", description = "Get a nonce for Sui wallet authentication")
    fun getNonce(@RequestParam("address") address: String): ResponseEntity<Any> {
        val nonce = "Sign this message to authenticate with TBook: ${System.currentTimeMillis()}"
        nonceService.addNonce(address.lowercase(), nonce)
        return ResponseEntity.ok(nonce)
    }

    @PostMapping("login")
    @Operation(summary = "Login with Sui wallet", description = "Login with Sui wallet address and signature")
    fun login(@RequestBody request: SuiLoginRequest): ResponseEntity<Any> {
        val nonce = nonceService.getAndInvalidateNonce(request.address.lowercase())
            ?: return ResponseEntity.badRequest().body(mapOf("message" to "Invalid nonce", "code" to 4001))

        val isSignatureValid = suiService.verifySuiSignature(
            request.address, request.publicKey, request.signature, nonce
        )
        if (!isSignatureValid) {
            return ResponseEntity.badRequest().body(mapOf("message" to "Invalid signature", "code" to 4002))
        }

        var isLogin = false
        val idPrincipal = SecurityContextHolder.getContext().authentication.principal.toString()
        if (idPrincipal == "anonymousUser") isLogin = true

        val userSui = userService.getSuiWalletByAddress(request.address)
        if (isLogin) {
            return withLogin(userSui, request.address, request.publicKey)
        }

        val user = userService.getUserByPrincipal(idPrincipal)!!
        if (user.userId == userSui?.userId) {
            return withLogin(userSui, request.address, request.publicKey)
        }
        if (userSui != null) {
            val passportA = PassportAccounts(
                userId = user.userId,
                evmAddress = user.evm.evmWallet ?: "",
                tonAddress = user.ton.tonWallet ?: "",
                suiAddress = user.sui.suiWallet ?: "",
                twitterName = user.twitterName,
                dcName = user.dcName,
                tgName = user.tgName
            )
            val suiUser = userService.getUserById(userSui.userId)!!
            val passportB = PassportAccounts(
                userId = suiUser.userId,
                evmAddress = suiUser.evm.evmWallet ?: "",
                tonAddress = suiUser.ton.tonWallet ?: "",
                suiAddress = suiUser.sui.suiWallet ?: "",
                twitterName = suiUser.twitterName,
                dcName = suiUser.dcName,
                tgName = suiUser.tgName
            )

            return ResponseEntity.ok(
                SocialAccountBound(
                    "This wallet is already bound to another user",
                    "SUI",
                    request.address,
                    passportA,
                    passportB
                )
            )
        }

        val bindResult = userService.bindSuiWallet(user.userId, request.address, request.publicKey)
        if (!bindResult) {
            return ResponseEntity.badRequest().body(mapOf("message" to "Failed to bind wallet", "code" to 4002))
        }

        return ResponseEntity.ok(mapOf("code" to 200, "user" to UserSuiDto(request.address, true)))
    }

    private fun withLogin(current: UserSui?, wallet: String, publicKey: String): ResponseEntity<Any> {
        if (current != null) {
            val cookie = Jwt.buildCookie(current.userId, "sui")
            return ResponseEntity.ok().header("Set-Cookie", cookie.toString()).body(
                mapOf("code" to 200, "user" to UserSuiDto(current.suiWallet, true))
            )
        } else {
            val userId = userService.registerSuiUser(wallet, publicKey)
            val cookie = Jwt.buildCookie(userId, "sui")
            return ResponseEntity.ok().header("Set-Cookie", cookie.toString()).body(
                mapOf("code" to 200, "user" to UserSuiDto(wallet, true))
            )
        }
    }

    @GetMapping("sbt/info/{campaignId}")
    // 根据campaign id获取project下的其他sui sbt、以及整体的trending sbt
    fun getSuiSbtInfos(@PathVariable campaignId: Long): ResponseEntity<Any> {
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(address)!!
        val auth = SecurityContextHolder.getContext().authentication as? com.rewardoor.app.auth.AddressAuthentication
        val chain = auth?.chain
        val userSui = userService.getSuiWalletByUserId(user.userId)
        if (!userSui.binded) {
            return ResponseEntity.ok(
                mapOf("code" to 400, "message" to "Please bind your Sui wallet first")
            )
        }
        val campaign = campaignService.getCampaignById(campaignId)!!
        val projectId = campaign.projectId
        val otherSbts = suiSbtRepository.getSuiSbtRewardByProjectId(projectId)?.filter { it.campaignId != campaignId }
        val trendingTop7SbtInfos = participantRepository.getTop7ClaimedSuiRewards()
        val trendingTop7Sbts = mutableListOf<SuiSbtReward>()
        for (sbt in trendingTop7SbtInfos) {
            val sbtId = sbt.first
            val sbtReward = suiSbtRepository.getSuiSbtRewardById(sbtId)
            if (sbtReward != null && sbtReward.campaignId != campaignId) {
                trendingTop7Sbts.add(sbtReward)
            }
        }
        return ResponseEntity.ok(
            mapOf(
                "code" to 200, "sbtInfos" to SuiProjectSbtInfos(
                    otherSbts,
                    trendingTop7Sbts
                )
            )
        )
    }

    @GetMapping("unbind")
    fun unbindSui(): ResponseEntity<Any> {
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(address)!!
        val auth = SecurityContextHolder.getContext().authentication as? com.rewardoor.app.auth.AddressAuthentication
        val chain = auth?.chain
        if (chain == null || chain.lowercase() != "sui") {
            return ResponseEntity.ok("Only sui wallet can unbind sui")
        }
        val userSui = userService.getSuiWalletByUserId(user.userId)
        if (!userSui.binded) {
            return ResponseEntity.ok(
                mapOf("code" to 400, "message" to "Please bind your Sui wallet first")
            )
        }
        val deleteCnt = userService.unbindSuiUser(user.userId)
        return ResponseEntity.ok(
            mapOf("code" to 200, "message" to "Unbind success", "cnt" to deleteCnt)
        )
    }

    @Transactional
    @PostMapping("preCreate")
    fun preCreateSbt(@RequestBody request: SuiSbtReward): ResponseEntity<Any> {
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(address)!!
        val auth = SecurityContextHolder.getContext().authentication as? com.rewardoor.app.auth.AddressAuthentication
        val chain = auth?.chain
        if (chain == null || chain.lowercase() != "sui") {
            return ResponseEntity.ok("Only sui wallet can create sui SBT")
        }
        val userSui = userService.getSuiWalletByUserId(user.userId)
        if (!userSui.binded) {
            return ResponseEntity.ok(
                mapOf("code" to 400, "message" to "Please bind your Sui wallet first")
            )
        }
        // 提前创建sbt reward，获取sbt id
        val sbtId = idGenerator.getNewId()
        val sbt = SuiSbtReward(
            suiSbtId = sbtId,
            sbtName = request.sbtName,
            sbtDesc = request.sbtDesc,
            sbtUrl = request.sbtUrl,
            projectId = request.projectId,
            creatorId = user.userId
        )
        suiSbtRepository.createSuiSbt(sbt)
        request.suiSbtId = sbtId
        val suiSbtSync = SuiSbtSync(
            sbtId = sbtId,
            projectId = request.projectId,
            sbtName = request.sbtName,
            sbtDesc = request.sbtDesc,
            sbtUrl = request.sbtUrl,
            objectId = "" // create时没有objectId
        )
        val syncResult = suiService.syncSuiSbt(suiSbtSync)
        val activityId = syncResult.suiSbtActivityId
        val suiSyncCheck = TonSyncHistory(
            projectId = request.projectId,
            title = request.sbtName,
            description = request.sbtDesc,
            startDate = Instant.now(),
            endDate = Instant.now(),
            sbtCollectionTitle = request.sbtName,
            sbtItemTitle = request.sbtName,
            sbtCollectionDesc = request.sbtDesc,
            sbtDesc = request.sbtDesc,
            syncAt = Instant.now(),
            sbtImage = request.sbtUrl,
            activityId = activityId,
            sbtId = sbtId,
            taskCategory = request.taskCategory,
            checkStatus = 1,
            networkId = 1
        )
        val h = tonSyncService.createSBTCheck(suiSyncCheck)
        val now = System.currentTimeMillis()
        val sign = suiService.signSbtCollection(
            request.sbtName,
            request.sbtDesc,
            request.sbtUrl,
            activityId,
            userSui.suiWallet!!,
            now
        )
        val createCollectionResponse = CreateCollectionResponse(
            request.sbtName,
            request.sbtDesc,
            request.sbtUrl,
            activityId.toString(),
            now,
            sign,
            h
        )
        return ResponseEntity.ok(
            mapOf("code" to 200, "result" to createCollectionResponse)
        )
    }


    @PostMapping("sbt/sync")
    fun createSbt(@RequestBody request: SuiSbtSync): ResponseEntity<Any> {
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(address)!!
        val userSui = userService.getSuiWalletByUserId(user.userId)
        if (!userSui.binded) {
            return ResponseEntity.ok(
                mapOf("code" to 400, "message" to "Please bind your Sui wallet first")
            )
        }
        return ResponseEntity.ok(
            suiService.syncSuiSbt(request)
        )
    }

    @Transactional
    @GetMapping("sbtId")
    fun getPreviewSbtById(@RequestParam sbtId: Long): SimpleResponseEntity<SuiSbtReward>? {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val sbtReward = suiSbtRepository.getSuiSbtRewardById(sbtId)
        val sbtCheckStatus = tonSocietySyncRepository.getTonSyncCheckBySBTId(sbtId)?.checkStatus ?: 0
        if (sbtReward != null) {
            sbtReward.checkStatus = sbtCheckStatus
            val sbtActivityId = suiSbtRepository.getSuiSbtSyncBySbtId(sbtId)?.suiSbtActivityId ?: 0
            sbtReward.suiSbtActivityId = sbtActivityId
            return SimpleResponseEntity.success("OK", sbtReward)
        }
        return SimpleResponseEntity.failed("Failed", null)
    }

    @PostMapping("sbt/claim")
    fun claimSbt(@RequestBody request: SuiClaimSbtRequest): ResponseEntity<Any> {
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(address)!!
        val userSui = userService.getSuiWalletByUserId(user.userId)
        if (!userSui.binded) {
            return ResponseEntity.ok(
                mapOf("code" to 400, "message" to "Please bind your Sui wallet first")
            )
        }
        val name = "TBook SBT"
        val description = "This is a claim for TBook SBT"
        val url = "https://static.tbook.vip/tb_intro.jpg"
        val sbtId = 32133L
        val suiSbtId = request.sbtId
        val credentialId = request.credentialId
        val credential = credentialService.getCredentialById(credentialId)!!
        val groupId = credential.groupId
//        val isCredentialVerified = credentialGroupService.verifyCredential(user, null, credential)
//        if (!isCredentialVerified) {
//            return ResponseEntity.ok(
//                mapOf(
//                    "code" to 400,
//                    "status" to "error",
//                    "message" to "Task Not Finished"
//                )
//            )
//        }
        val sbtClaimRes = signSbtClaim(name, description, url, sbtId, userSui.suiWallet!!)
        wiseScoreService.addUserReward(4, suiSbtId, user.userId, groupId, 3)
        return ResponseEntity.ok(
            mapOf(
                "code" to 200,
                "status" to "success",
                "sbtClaimResult" to sbtClaimRes
            )
        )
    }

    @PostMapping("sbt/collectionId")
    fun updateCollectionId(@RequestBody request: UpdateCollectionIdRequest): Any {
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(address)!!
        val sbtSync = suiSbtRepository.getSuiSbtSyncById(request.sbtId)
        if (sbtSync == null) {
            throw ResponseStatusException(HttpStatus.NOT_FOUND, "SBT reward not found")
        }

        val suiSbt = suiSbtRepository.getSuiSbtRewardById(sbtSync.sbtId)
        if (suiSbt == null) {
            throw ResponseStatusException(HttpStatus.NOT_FOUND, "SBT reward not found")
        }
        if (suiSbt.creatorId != user.userId) {
            throw ResponseStatusException(HttpStatus.FORBIDDEN, "You are not the creator of this SBT reward")
        }

        suiService.updateSbtCollectionInfo(sbtSync.suiSbtActivityId, suiSbt.suiSbtId, request.collectionId)

        return ResponseEntity.ok(
            mapOf("code" to 200, "message" to "Collection ID updated successfully")
        )
    }

    private fun signSbtClaim(
        name: String, description: String, url: String,
        sbtId: Long, address: String
    ): SuiClaimSbtResponse {
        val pureAddress = if (address.startsWith("0x")) address.substring(2) else address
        val signStr = "$name$description$pureAddress$sbtId"
        val privateKey = HexFormat.of().parseHex(secretKey)
        val signBytes: ByteArray = Ed25519.sign(privateKey, signStr.toByteArray())
        val signature = HexFormat.of().formatHex(signBytes)
        val claim = SuiClaimSbtResponse(
            name = name,
            description = description,
            url = url,
            sbtId = sbtId.toString(),
            signature = signature
        )
        return claim
    }
}

class SuiLoginRequest(
    val address: String,
    val signature: String,
    @JsonSetter(nulls = Nulls.AS_EMPTY)
    val publicKey: String = "",
)

class SuiClaimSbtRequest(
    val credentialId: Long,
    val sbtId: Long,
)

class SuiClaimSbtResponse(
    val name: String,
    val description: String,
    val url: String,
    val sbtId: String,
    val signature: String,
)

class CreateCollectionResponse(
    val name: String,
    val description: String,
    val url: String,
    val activityId: String,
    val ts: Long,
    val signature: String,
    val syncHistory: TonSyncHistory
)

class UpdateCollectionIdRequest(
    val collectionId: String,
    val sbtId: Long,
)

class SuiProjectSbtInfos(
    val otherSbts: List<SuiSbtReward>? = emptyList(),
    val trendingSbts: List<SuiSbtReward>? = emptyList()
)