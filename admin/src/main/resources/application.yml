
spring:
  jackson:
    deserialization:
      read-date-timestamps-as-nanoseconds: false
  web:
    resources:
      static-locations:
        - file:src/main/resources/static/
        - classpath:/static/
  data:
    redis:
      port: 6379
      host: tbook-stag.8sthme.ng.0001.apse1.cache.amazonaws.com
  mail:
    host: smtp.gmail.com
    port: 587
    username: <EMAIL>
    password: igsqnmdkhxlggpfo
    properties:
      mail:
        smtp:
          starttls:
            enable: true
          auth: true
          enable: true

datasource:
  master:
    url: **********************************************************************************************************************************
    username: rewardoor
    password: YMJDWc6R3EArNiBB
    hikari:
      dataSourceProperties:
        cachePrepStmts: true
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        useServerPrepStmts: true
        useLocalSessionState: true
        useLocalTransactionState: true
        rewriteBatchedStatements: true
        cacheResultSetMetadata: true
        cacheServerConfiguration: true
        elideSetAutoCommits: true
        maintainTimeStats: false
        initializationFailTimeout: 500
      maximum-pool-size: 10
    type: com.zaxxer.hikari.HikariDataSource

r2:
  endpoint: https://04046c63b7c3860b6434138078ee49e7.r2.cloudflarestorage.com/rd-nft
  publicUrl: https://static.tbook.vip/
  accountId: 04046c63b7c3860b6434138078ee49e7
  accessKey: ${r2_access_key:abc}
  secretKey: ${r2_secret_key:def}
twitter:
  ut_key: o7ZFzpSQlSmRYHi612DGxgjsmhz263vDRbp35frwEcvyS|********-PShUS3dHZ8dhKds3z9mdzDNbQbSAvq08MRMbRtcHa
  api:
    id: NGNvM0Z1WlpmM0wyYkJoZ2gxQmo6MTpjaQ
    secret: NGctQalrX3gPnkASa2P_xAT8rxxI7FZr8BRFSnYb5IejW_xjNa
    consumer_key: *************************
    consumer_secret: enhVSJLnj6CtmmHj5YfSQlGrAM3a38Bvt9JRtWi6h3gpFE3Gtn
    access_token: ********-jRTJrZoaeyic7aRCRym0yQ2yCXUovc9xIvXd6RAxs
    access_token_secret: KQ4c7zUSG3ksQn3Auej5IhVg6bJDR20Saj5xlVChy5cY6
    bearer: AAAAAAAAAAAAAAAAAAAAAF3%2BcAEAAAAAe%2Fq0eaPSesDTscu6o5er2X16D6o%3Dkr0K0pnLpApRzApuq4r4bvC7NMg3YjeQn5Bv7sqr9uqdGHjpsl

tg:
  bot_id: **********
  bot_token: **********:AAH-cmeiJy39d6FsrfArB-SDFpcnWFlVqLg
  check_bot_token: **********:AAHDuSR_gDR3jsMy5jnjS1LT1iSu5HjKE4g
  sign_hook_url: https://rd-api-staging.tbook.com/tg_sign/webhook
  mini_app:
    bot_name: tbook01_bot
    app_name: tbook
    token: 6364113882:AAGKqnqRqo5am22knQQ-mgDF_-J0YKGDzLY
    game_build_token: 123
    ai_agent_token: 7685309945:AAEKjvItBj_Bz3FbcmoHDmSjgqEneHtAU7M
    webhook_url: https://rd-api-staging.tbook.com/tg/webhook
    wise_start_param: WzMsIk1MWFNISiJd
  x-api-key: 63956d1b10345d7796c5526fffa73f486dd3547728bb6000ca630c6ed0dffb3b
#  x-api-key: 6b3d1ffada023700836fbc44c32810d56bd79a24014ccde487a3c391961bbaaa
  ton-x-api-key: cd9d7aff-1521-4c16-819e-57e2125e6485
  ton-x-partner-id: tbook

chainBase:
  x-api-key: 2fOoC404XegbRcQoC9HX554CzSW

dc:
  app_id: 1146414186566537288
  app_secret: SFBIDtL-Nc1KQr_IJMiK4wGPvvcZBBk4
  bot_token: MTE0NjQxNDE4NjU2NjUzNzI4OA.GcdN6S.LFtP6uPnVcNYtw6ftmIjQHZ85aPgVMnXb0WOes
  callback: https://campaign-staging.tbook.com/dc_callback

coinMarket:
  api_key: 515f30bf-3516-4dc7-8f67-12c67198324d

management:
  endpoints:
    web:
      exposure:
        include: health,info,prometheus,mappings,metrics
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
      logging:
        slow-indicator-threshold: 2s

logging:
  config: classpath:log4j2.xml

cors:
  origins: https://ton-rewardoor-staging.tbook.com,https://sui-rewardoor-staging.tbook.com,https://evm-rewardoor-staging.tbook.com,https://op-staging.tbook.com,https://rd-api-staging.tbook.com,https://campaign-staging.tbook.com,https://rewardoor-staging.tbook.com,https://rewardoor-api.fly.dev,https://my.tbook.com,https://tbook.vip,https://app.tbook.vip,https://api.tbook.vip,https://tbook.com,https://www.tbook.com,https://app.tbook.com,https://api.tbook.com,https://app-staging.tbook.com,https://home-staging.tbook.com,https://main.d317sxkzo6gdsi.amplifyapp.com,https://rd-admin-api-staging.tbook.com

manager:
  private_key: ${manager_private_key:abc}
  space_station_contract_address: ${space_station_contract:******************************************}
  chain_id: ${manager_chainid:420}

contracts:
  file: contracts_testing.json

tenant:
  go-plus:
    id: 1
    url: https://test-secwarex-api.ansuzsecurity.com/open/v1/task/finish
    token: test

contract_caller:
  url: ${contract_caller_url:https://contract-caller-staging.tbook.com}
  secret: ${contract_caller_secret:abc}

zklogin:
  enoki_url: https://api.enoki.mystenlabs.com/v1
  enoki_api_key: enoki_apikey_1380828c5244c4aed97488e2d6f092e7
  proof_service_url: https://prover-dev.mystenlabs.com/v1
  google:
    client_id: 179687049344-ft7mu64ctnf5lnurnli762qsal7dnufa.apps.googleusercontent.com
    client_secret: 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
    redirect_uri: https://campaign-staging.tbook.com

projects:
  need_evm: ${evm_projects:326105041717,192936330355,353044501956}

home:
  projects: 154283610009,227698820763,359984641969,227548350746,269686381276
  campaigns: 386641862269,415988872776,416007082781,416009922785
  admins: ******************************************,******************************************,******************************************

game_drop:
  private_key: ${game_drop_private_key:0xd5c03e98cac31e7774640c587bfe4c3f00104eb2ef9da04732132c0e86098540}
  contract: ${game_drop_contract:0x165FAC0A9f3E9d4a3F568Bd57e8580ED1c2EC010}
  chain_id: ${game_drop_chainid:11155111}
  domain: ${game_drop_domain:GameAirdrop}

wise_task:
  channel: tbooktest0001
  group: tbooktest111

ton:
  sync:
    mail_receivers: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>

airdrop:
  ton_secret_key: ${ton_airdrop_secret_key:abc}
server:
  port: 8090
sui-verify:
  host: ${sui_verify_host:http://127.0.0.1:3003/verify}
  auth: ${sui_verify_auth:}

---
spring:
  config:
    activate:
      on-profile: stag
  data:
    redis:
      port: 34523
      host: apn1-awake-whippet-34523.upstash.io
      password: 1992620018db4c6a8534ab60f3169897

datasource:
  master:
    url: ${datasource_master_url:#{'**********************************************************************************************************************************'}}
    username: rewardoor
    password: YMJDWc6R3EArNiBB

---
spring:
  config:
    activate:
      on-profile: local
  data:
    redis:
      port: 6379
      host: 127.0.0.1

tg:
  sign_hook_url: https://rd.100866.best/tg_sign/webhook
  bot_token: 7371641089:AAGb4Kqt3EzUUqfunuw7kqCu-LdXjixz5BY
  mini_app:
    token: **********:AAH-cmeiJy39d6FsrfArB-SDFpcnWFlVqLg
    webhook_url: https://rd.100866.best/tg/webhook

---
spring:
  config:
    activate:
      on-profile: prod
  data:
    redis:
      port: 6379
      host: tbook-prod.8sthme.ng.0001.apse1.cache.amazonaws.com

cors:
  origins: https://ton.tbook.com,https://sui.tbook.com,https://evm.tbook.com,https://www.tbook.com,https://op.tbook.com,https://archloot.tbook.com,https://carry.tbook.com,https://rd-api.tbook.com,https://m.tbook.com,https://i.tbook.com,https://rewardoor-staging.tbook.com,https://home-staging.tbook.com/,https://my-staging.tbook.com,https://staging.tbook.com,https://app-staging.tbook.com,https://testing.tbook.vip,https://app-testing.tbook.vip,https://api-testing.tbook.vip,https://testing.tbook.com,https://app-testing.tbook.com,https://api-testing.tbook.com,https://grant-testing.tbook.com,https://app.tbook.com,https://api.tbook.com,https://main.d317sxkzo6gdsi.amplifyapp.com,https://rd-admin-api-staging.tbook.com

datasource:
  master:
    url: ${datasource_master_url:#{'*********************************************************************************************************************************'}}
    username: ${database_username:abc}
    password: ${database_password:def}
    hikari:
      leak-detection-threshold: 60000
      maximum-pool-size: 20
      connection-timeout: 30000

tg:
  bot_id: **********
  bot_token: ${tg_bot_token:123}
  check_bot_token: ${tg_check_bot_token:123}
  sign_hook_url: https://rd-api.tbook.com/tg_sign/webhook
  mini_app:
    token: ${tg_mini_app_token:123}
    webhook_url: https://rd-api.tbook.com/tg/webhook
    bot_name: tbook_incentive_bot
    app_name: tbook
    wise_start_param: WzMsIk1MWFNISiJd

contracts:
  file: contracts.json

tenant:
  go-plus:
    id: 3966940611581
    url: https://api.secwarex.io/open/v1/task/finish
    token: C6ebAfQKeDht3OOigsQ30tOTafShNQkZ

projects:
  need_evm: 3966940611581,3866689611533,45284759495770,46160171511957

home:
  projects: 42432389383981,3966940611581
  campaigns: 42434689384039,3994128311607
  admins: ******************************************,******************************************,******************************************

etherScan:
  api-key: **********************************

server:
  undertow:
    threads:
      worker: 200

wise_task:
  channel: tbookincentive
  group: tbookofficialgroup

springdoc:
  swagger-ui:
    enabled: false
