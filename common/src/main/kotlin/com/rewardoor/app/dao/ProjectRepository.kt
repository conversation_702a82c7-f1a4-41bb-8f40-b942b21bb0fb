package com.rewardoor.app.dao

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.rewardoor.app.dao.tables.*
import com.rewardoor.model.*
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import javax.xml.transform.Templates

@Repository
@Transactional
class ProjectRepository(val om: ObjectMapper) {
    fun addProject(project: Project, creatorId: Long) {
        TBProject.insert {
            it[projectId] = project.projectId
            it[projectName] = project.projectName
            it[projectUrl] = project.projectUrl
            it[avatarUrl] = project.avatarUrl
            it[TBProject.creatorId] = creatorId
            it[tags] = om.writeValueAsString(project.tags)
            it[theme] = project.theme
            it[projectDescription] = project.projectDescription
            it[chain] = project.chain
            it[goal] = project.goal
            it[tgHandle] = project.tgHandle
        }
    }

    fun update(project: Project): Int {
        return TBProject.update({ TBProject.projectId eq project.projectId }) {
            it[projectId] = project.projectId
            it[projectName] = project.projectName
            it[projectUrl] = project.projectUrl
            it[avatarUrl] = project.avatarUrl
            it[creatorId] = creatorId
            it[tags] = om.writeValueAsString(project.tags)
            it[theme] = project.theme
            it[projectDescription] = project.projectDescription
            it[websiteUrl] = project.websiteUrl
            it[telegramUrl] = project.telegramUrl
            it[discordLink] = project.discordLink
            it[banner] = project.banner
            it[emptyCampaignText] = project.emptyCampaignText
            it[projectCustomItemA] = project.projectCustomItemA
            it[layerOneList] = om.writeValueAsString(project.layerOneList)
        }
    }

    fun updateCheckStatus(projectId: Long, newCheckStatus: Int): Int {
        return TBProject.update({ TBProject.projectId eq projectId }) {
            it[checkStatus] = newCheckStatus
        }
    }

    fun getProjectById(projectId: Long): Project? {
        return TBProject.select { TBProject.projectId eq projectId }.limit(1).map(::mapProject).firstOrNull()
    }

    fun deleteProjectById(projectId: Long): Int {
        return TBProject.deleteWhere { TBProject.projectId eq projectId }
    }

    fun getAllProjects(): List<Project> {
        return TBProject.selectAll().map(::mapProject)
    }

    fun getProjectsById(ids: List<Long>): List<Project> {
        return TBProject.select { TBProject.projectId.inList(ids) }.map(::mapProject)
    }

    fun getProjectByUrl(url: String): Project? {
        return TBProject.select { TBProject.projectUrl eq url }.limit(1).map(::mapProject).firstOrNull()

        fun getProjectByUrlAndChain(url: String, chain: String?): Project? {
            return if (chain.isNullOrBlank()) {
                TBProject.select { TBProject.projectUrl eq url }
                    .limit(1)
                    .map(::mapProject)
                    .firstOrNull()
            } else {
                TBProject.select { (TBProject.projectUrl eq url) and (TBProject.chain eq chain) }
                    .limit(1)
                    .map(::mapProject)
                    .firstOrNull()
            }
        }
    }

    fun getProjectByName(name: String): Project? {
        return TBProject.select { TBProject.projectName eq name }.limit(1).map(::mapProject).firstOrNull()
    }

    fun getUserProjects(userId: Long): List<Project> {
        return TBProject.select { TBProject.creatorId eq userId }.map(::mapProject)
    }

    fun mapProject(r: ResultRow): Project {
        return Project(
            r[TBProject.projectId],
            r[TBProject.companyId],
            r[TBProject.projectName],
            r[TBProject.projectUrl],
            r[TBProject.avatarUrl].ifEmpty { "https://static.tbook.vip/img/a5d46dcb250f4579b3591773824dd92d" },
            r[TBProject.creatorId],
            om.readValue<List<String>>(r[TBProject.tags]),
            r[TBProject.theme],
            r[TBProject.projectDescription].orEmpty(),
            r[TBProject.websiteUrl],
            r[TBProject.telegramUrl],
            r[TBProject.twitterLink],
            r[TBProject.telegramLink],
            r[TBProject.discordLink],
            r[TBProject.banner],
            r[TBProject.emptyCampaignText],
            r[TBProject.projectCustomItemA],
            r[TBProject.layerOneList]?.let {
                try {
                    om.readValue<List<Long>>(it)
                } catch (e: Exception) {
                    emptyList()
                }
            } ?: emptyList(),
            r[TBProject.chain],
            r[TBProject.goal],
            r[TBProject.tgHandle],
            r[TBProject.checkStatus]
        )
    }

    fun getProjectCreator(projectId: Long): Long? {
        return TBProject.select { TBProject.projectId eq projectId }.limit(1).map { it[TBProject.creatorId] }
            .firstOrNull()
    }

    fun getProjectListByCompany(companyId: Long): List<Project> {
        return TBProject.select { TBProject.companyId eq companyId }.map(::mapProject)
    }

    fun getProjectListByCompanyWithLayerOneId(companyId: Long, layerOneId: Long): List<Project> {
        return TBProject
            .select {
                (TBProject.companyId eq companyId) and
                        (TBProject.layerOneList.isNotNull()) and
                        CustomFunction<Boolean>(
                            "JSON_VALID",
                            BooleanColumnType(),
                            TBProject.layerOneList
                        ).eq(true) and
                        CustomFunction<Boolean>(
                            "JSON_CONTAINS",
                            BooleanColumnType(),
                            TBProject.layerOneList,
                            stringLiteral(layerOneId.toString())
                        ).eq(true)
            }
            .map(::mapProject)
    }

    fun addHomeBanners(banners: List<BannerSetting>) {
        TBHomeBanner.batchInsert(banners) { banner ->
            this[TBHomeBanner.imageUrl] = banner.bannerImageUrl
            this[TBHomeBanner.link] = banner.bannerLink
            this[TBHomeBanner.createdAt] = LocalDateTime.now() // Set current timestamp for createdAt
            this[TBHomeBanner.updatedAt] = LocalDateTime.now()// Set current timestamp for updatedAt
        }
    }

    fun getHomeBanners(): List<BannerSetting> {
        return TBHomeBanner.selectAll().map(::mapHomeBanner)
    }

    fun deleteHomeBanner() {
        TBHomeBanner.deleteAll()
    }

    fun mapHomeBanner(r: ResultRow): BannerSetting {
        return BannerSetting(
            r[TBHomeBanner.imageUrl],
            r[TBHomeBanner.link]
        )
    }

    fun addHomeCampaigns(campaignSettings: List<CampaignSetting>) {
        TBHomeCampaign.batchInsert(campaignSettings) { campaignSetting ->
            this[TBHomeCampaign.campaignId] = campaignSetting.campaignId
            this[TBHomeCampaign.campaignTitle] = campaignSetting.campaignTitle
            this[TBHomeCampaign.imageUrl] = campaignSetting.imageUrl
            this[TBHomeCampaign.createdAt] = LocalDateTime.now() // Set current timestamp for createdAt
            this[TBHomeCampaign.updatedAt] = LocalDateTime.now()// Set current timestamp for updatedAt
        }
    }

    fun getHomeCampaigns(): List<CampaignSetting> {
        return TBHomeCampaign.selectAll().map(::mapHomeCampaign)
    }

    fun deleteHomeCampaign() {
        TBHomeCampaign.deleteAll()
    }

    fun mapHomeCampaign(r: ResultRow): CampaignSetting {
        return CampaignSetting(
            r[TBHomeCampaign.campaignId],
            r[TBHomeCampaign.campaignTitle],
            r[TBHomeCampaign.imageUrl]
        )
    }

    fun addHomeSBTs(sbtSettings: List<SbtSetting>) {
        TBHomeSBT.batchInsert(sbtSettings) { sbtSetting ->
            this[TBHomeSBT.sbtId] = sbtSetting.sbtId
            this[TBHomeSBT.sbtTitle] = sbtSetting.sbtTitle
            this[TBHomeSBT.activityId] = sbtSetting.sbtActivityId
            this[TBHomeSBT.createdAt] = LocalDateTime.now() // Set current timestamp for createdAt
            this[TBHomeSBT.updatedAt] = LocalDateTime.now()// Set current timestamp for updatedAt
        }
    }

    fun getHomeSBTs(): List<SbtSetting> {
        return TBHomeSBT.selectAll().map(::mapHomeSBT)
    }

    fun deleteHomeSbt() {
        TBHomeSBT.deleteAll()
    }

    fun mapHomeSBT(r: ResultRow): SbtSetting {
        return SbtSetting(
            r[TBHomeSBT.sbtId],
            r[TBHomeSBT.sbtTitle],
            r[TBHomeSBT.activityId]
        )
    }

    fun addHomeTemplates(template: HomeProjectTemplate) {
        TBHomeProjectTemplate.insert {
            it[templateName] = template.templateName
            it[bannerTitle] = template.bannerTitle
            it[bannerPicUrl] = template.bannerPicUrl
            it[bannerSbtIds] = om.writeValueAsString(template.bannerSbtIds)
            it[bannerSubTitle] = template.bannerSubTitle
            it[middleTitle] = template.middleTitle
            it[middleDescription] = template.middleDescription
            it[tonHeroCampaignId] = template.tonHeroCampaignId
            it[tonHeroSbtPicUrl] = template.tonHeroSbtPicUrl
            it[tonHeroTitle] = template.tonHeroTitle
            it[tonHeroDescription] = template.tonHeroDescription
            it[campaignIds] = om.writeValueAsString(template.campaignIds)
        }
    }

    fun getHomeTemplatesByName(name: String): HomeProjectTemplate? {
        return TBHomeProjectTemplate.select { TBHomeProjectTemplate.templateName eq name }.map(::mapHomeTemplates)
            .firstOrNull()
    }

    fun deleteHomeTemplatesByName(name: String): Int {
        return TBHomeProjectTemplate.deleteWhere { TBHomeProjectTemplate.templateName eq name }
    }

    fun mapHomeTemplates(r: ResultRow): HomeProjectTemplate {
        return HomeProjectTemplate(
            r[TBHomeProjectTemplate.templateName],
            r[TBHomeProjectTemplate.bannerTitle],
            r[TBHomeProjectTemplate.bannerPicUrl],
            om.readValue<List<Long>>(r[TBHomeProjectTemplate.bannerSbtIds]),
            r[TBHomeProjectTemplate.bannerSubTitle],
            r[TBHomeProjectTemplate.middleTitle],
            r[TBHomeProjectTemplate.middleDescription],
            r[TBHomeProjectTemplate.tonHeroCampaignId],
            r[TBHomeProjectTemplate.tonHeroSbtPicUrl],
            r[TBHomeProjectTemplate.tonHeroTitle],
            r[TBHomeProjectTemplate.tonHeroDescription],
            om.readValue<List<Long>>(r[TBHomeProjectTemplate.campaignIds])
        )
    }

    fun addHomeProjects(projectSettings: List<ProjectSetting>) {
        TBHomeProject.batchInsert(projectSettings) { projectSetting ->
            this[TBHomeProject.projectId] = projectSetting.projectId
            this[TBHomeProject.projectTitle] = projectSetting.projectTitle
            this[TBHomeProject.createdAt] = LocalDateTime.now() // Set current timestamp for createdAt
            this[TBHomeProject.updatedAt] = LocalDateTime.now()// Set current timestamp for updatedAt
        }
    }

    fun getHomeProjects(): List<ProjectSetting> {
        return TBHomeProject.selectAll().map(::mapHomeProject)
    }

    fun deleteProject() {
        TBHomeProject.deleteAll()
    }

    fun mapHomeProject(r: ResultRow): ProjectSetting {
        return ProjectSetting(
            r[TBHomeProject.projectId],
            r[TBHomeProject.projectTitle]
        )
    }

}