package com.rewardoor.app.services

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.rewardoor.IdGenerator
import com.rewardoor.app.dao.*
import com.rewardoor.app.dto.CampaignReportDto
import com.rewardoor.model.*
import org.json.JSONObject
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.env.Environment
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.security.SecureRandom
import java.util.*

@Service
@Transactional
class ProjectService(
    val projectRepo: ProjectRepository,
    val projectTokenRepo: ProjectTokenRepository,
    val resultReportRepo: ResultReportRepository,
    val idGenerator: IdGenerator,
    val campaignRepo: CampaignRepository,
    val campaignService: CampaignService,
    val adminService: AdminService,
    val participantRepo: ParticipantRepository,
    val userRepo: UserRepository,
    val projectExternalConfigRepo: ProjectExternalRepository,
    val credentialRepo: CredentialRepository,
    val credentialGroupService: CredentialGroupService,
    val pointRepo: PointRepository,
    val sbtRewardRepo: SBTRewardRepository,
    @Value("\${projects.need_evm}") val needEvmProjects: MutableList<Long>,
    @Value("\${home.projects}") val homeProjectIds: MutableList<Long>,
    @Value("\${home.campaigns}") val homeCampaignIds: MutableList<Long>,
    @Value("\${ton.sync.mail_receivers}") private val mailReceivers: MutableList<String>,
    private val redisTemplate: StringRedisTemplate,
    private val tonSocietySyncRepository: TonSocietySyncRepository,
    private val sbtWhiteListRepo: SBTWhiteListRepository,
    private val userTonRepository: UserTonRepository,
    private val credentialGroupRepository: CredentialGroupRepository,
    private val mailService: MailService,
    private val env: Environment
) {
    private val mapper = jacksonObjectMapper()

    fun addProject(project: Project, userId: Long, chain: String): Project {
        val projectId = idGenerator.getNewId()
        project.projectId = projectId
        projectRepo.addProject(project, userId)
        val p = if (env.activeProfiles.contains("prod")) "PROD" else "STAG"
        mailService.sendTBook(
            mailReceivers,
            "Create [$p] Project Check",
            "New Project:\n\n ${mapper.writeValueAsString(project)}"
        )
        val creator = userRepo.findUserById(userId)!!
        val adminWallet = if (chain == "sui") {
            creator.suiAddress
        } else if (chain == "ton") {
            creator.ton.tonWallet ?: ""
        } else if (chain == "evm") {
            creator.wallet
        } else {
            ""
        }
        val owner = Admin(
            userId = creator.userId,
            wallet = adminWallet,
            projectId = project.projectId,
            creatorId = creator.userId,
            isOwner = 1,
            status = 1
        )
        adminService.addAdmin(owner)
        return projectRepo.getProjectById(projectId)!!.also {
            it.evmRequire = projectNeedEvm(projectId)
            it.tonRequire = projectNeedTon(projectId)
        }
    }

    fun checkProject(projectId: Long, checkStatus: Int): Int {
        val existProject = projectRepo.getProjectById(projectId)
        if (existProject != null) {
            val updateCnt = projectRepo.updateCheckStatus(projectId, checkStatus)
            //不通过时，删除project
            if (checkStatus == 2 && existProject.checkStatus == 0) {
                projectRepo.deleteProjectById(projectId)
            }
            return updateCnt
        }
        return 0
    }

    fun getCheckList(): List<Project> {
        val checkProjects = projectRepo.getAllProjects().filter { it.checkStatus == 0 }
        for (project in checkProjects) {
            val creatorId = project.creatorId
            val creator = userRepo.findUserById(creatorId)
            val chain = project.chain
            var creatorAddress = ""
            if (chain.equals("eth")) {
                creatorAddress = creator?.evm?.evmWallet ?: ""
            } else if (chain.equals("ton")) {
                creatorAddress = creator?.ton?.tonWallet ?: ""
            } else {
                // sui
                creatorAddress = creator?.suiAddress ?: ""
            }
            project.creatorAddress = creatorAddress
        }
        return checkProjects
    }

    fun getProject(projectId: Long): Project? {
        return projectRepo.getProjectById(projectId)?.also {
            it.evmRequire = projectNeedEvm(projectId)
            it.tonRequire = projectNeedTon(projectId)
        }
    }

    fun getProjectByUrl(projectUrl: String): Project? {
        return projectRepo.getProjectByUrl(projectUrl)?.also {
            it.evmRequire = projectNeedEvm(it.projectId)
            it.tonRequire = projectNeedTon(it.projectId)
        }
    }

    fun getProjectByName(projectName: String): Project? {
        return projectRepo.getProjectByName(projectName)?.also {
            it.evmRequire = projectNeedEvm(it.projectId)
            it.tonRequire = projectNeedTon(it.projectId)
        }
    }

    fun getUserProjects(userId: Long): List<Project> {
        return projectRepo.getUserProjects(userId).map {
            it.evmRequire = projectNeedEvm(it.projectId)
            it.tonRequire = projectNeedTon(it.projectId)
            it
        }
    }

    fun getProjectTokens(projectId: Long): List<String> {
        return projectTokenRepo.getProjectToken(projectId)
    }

    fun projectNeedEvm(projectId: Long): Boolean {
        return redisTemplate.opsForSet().isMember("projects.need_evm", projectId.toString()) ?: false
    }

    fun projectNeedTon(projectId: Long): Boolean {
        return redisTemplate.opsForSet().isMember("projects.need_ton", projectId.toString()) ?: false
    }

    fun generateAPIToken(projectId: Long, userId: Long): String {
        val newId = idGenerator.getNewId()
        val token = UUID.randomUUID().toString()
        projectTokenRepo.addProjectToken(newId, projectId, userId, token)
        return token
    }

    fun addResult(campaignReportDto: CampaignReportDto, token: String) {
        resultReportRepo.addResults(campaignReportDto, token)
    }

    fun getAllScheduledAndOnGoingCampaigns(): List<CampaignCard> {
        val campaigns = campaignRepo.getAllScheduledAndOnGoingCampaigns()!!
//        val campaignIds = listOf<Long>(************, ************, ************, ************)
        val campaignIds =
            listOf<Long>(************, ************, ************, ************, ************, ************)
        val filterCampaigns = campaigns.filter { campaignIds.contains(it.campaignId) }
        val campaignCards = mutableListOf<CampaignCard>()
        for (campaign in filterCampaigns) {
            val campaignId = campaign.campaignId
            val project = getProject(campaign.projectId)!!
            val campaignTotal = campaignService.getCampaignTotalById(campaignId)!!
            val points = campaignService.mergePointLists(campaignTotal)
            val participants = participantRepo.getAllParticipants(campaignId)!!
            val userIdList = participants.distinctBy { it.userId }.map { it.userId }
            val users = userRepo.getUsersByUserIdList(userIdList)!!
            var campaignCategory = 0
//            when (campaignId) {
//                ************, ************ -> campaignCategory = 3
//                ************ -> campaignCategory = 2
//                ************ -> campaignCategory = 1
//            }
            when (campaignId) {
                ************, ************ -> campaignCategory = 1
                ************ -> campaignCategory = 5
                ************, ************ -> campaignCategory = 3
            }
            val campaignCard = CampaignCard(
                campaignId = campaignId,
                title = campaign.title,
                name = campaign.name,
                picUrl = campaign.picUrl,
                description = campaign.description,
                status = campaign.status,
                campaignCategory = campaignCategory,
                project = project,
                users = users,
                points = points,
            )
            campaignCards.add(campaignCard)
        }
        return campaignCards
    }

    fun setHomeProjectsTemplates(homeSettings: HomeProjectsTemplateSettings): HomeProjectTemplate {
        val templateName = homeSettings.templateName
        if (templateName.isNotEmpty()) {
            projectRepo.deleteHomeTemplatesByName(templateName)
        }
        val homeProjectTemplate = HomeProjectTemplate(
            templateName = templateName,
            bannerTitle = homeSettings.banner?.title ?: "",
            bannerPicUrl = homeSettings.banner?.picUrl ?: "",
            bannerSbtIds = homeSettings.banner?.sbtIds ?: emptyList(),
            bannerSubTitle = homeSettings.banner?.subTitle ?: "",
            middleTitle = homeSettings.middle?.title ?: "",
            middleDescription = homeSettings.middle?.description ?: "",
            tonHeroCampaignId = homeSettings.tonHero?.campaignId ?: 0L,
            tonHeroSbtPicUrl = homeSettings.tonHero?.sbtPicUrl ?: "",
            tonHeroTitle = homeSettings.tonHero?.title ?: "",
            tonHeroDescription = homeSettings.tonHero?.description ?: "",
            campaignIds = homeSettings.campaignIds
        )
        projectRepo.addHomeTemplates(homeProjectTemplate)
        return homeProjectTemplate
    }

    fun getUserSbtClaimedTypeByUidAddressActivityId(userId: Long, tonAddress: String, activityId: Int): Int {
        // sbts 共用一个activity id，取最大的claim type
        val sbts = sbtRewardRepo.getSBTByActivityId(activityId)
        var maxClaimedType = 0
        for (sbt in sbts) {
            val rewardType = participantRepo.getUserSbtReward(sbt.sbtId, userId, sbt.groupId)?.claimType ?: 0
            if (rewardType > maxClaimedType) {
                maxClaimedType = rewardType
            }
        }
        return maxClaimedType
    }

    fun getSbtSettingById(sbtId: Long, userId: Long): SbtSetting? {
        val tonAddress = userTonRepository.getTonUserWalletByUserId(userId)?.tonWallet
        val sbtReward = sbtRewardRepo.getSBTById(sbtId)
        val tonSyncHistory = tonSocietySyncRepository.getTonSyncHistoryBySBTId(sbtId)
        if (sbtReward != null) {
            val project = getProject(sbtReward.projectId)
            val sbtCampaignId =
                credentialGroupRepository.getCredentialGroupById(sbtReward.groupId)?.campaignId ?: 0L
            val syncCampaignId = tonSyncHistory?.campaignId ?: 0
            val settingCampaignId = if (syncCampaignId > 0) syncCampaignId else sbtCampaignId
            val sbtSetting = SbtSetting(
                sbtId = sbtReward.sbtId,
                sbtTitle = tonSyncHistory?.sbtCollectionTitle ?: "",
                sbtActivityId = sbtReward.activityId,
                sbtName = sbtReward.name,
                picUrl = sbtReward.picUrl,
                activityUrl = sbtReward.activityUrl,
                campaignId = settingCampaignId,
                projectUrl = project?.projectUrl ?: "",
                claimType = getUserSbtClaimedTypeByUidAddressActivityId(
                    userId,
                    tonAddress ?: "",
                    sbtReward.activityId
                ),
                sbtDescription = tonSyncHistory?.sbtDesc ?: ""
            )
            return sbtSetting
        }
        return null
    }

    fun getHomeProjectsTemplates(name: String, userId: Long): HomeProjectsTemplateSettings? {
        val template = projectRepo.getHomeTemplatesByName(name)
        var tonAddress = ""
        if (userId != 0L) {
            tonAddress = userTonRepository.getTonUserWalletByUserId(userId)?.tonWallet ?: ""
        }
        if (template != null) {
//            val sbtIds = template.bannerSbtIds
            val sbtIds = mutableListOf<Long>()
            val campaignIds = template.campaignIds
            val sbtList = mutableListOf<SbtSetting>()
            val campaignList = mutableListOf<CampaignSetting>()
            if (campaignIds.isNotEmpty()) {
                for (id in campaignIds) {
                    val campaign = campaignRepo.getCampaignById(id)
                    val groups = credentialGroupService.getCredentialGroupByCampaignId(id)!!
                    var sbtImageUrl = ""
                    for (group in groups) {
                        val sbtRewards = sbtRewardRepo.getSBTByGroupId(group.id)
                        if (sbtRewards.isNotEmpty()) {
                            for (sbtReward in sbtRewards) {
                                sbtIds.add(sbtReward.sbtId)
                                sbtImageUrl = sbtReward.picUrl
                            }
                        }
                    }
                    if (campaign != null) {
                        val project = getProject(campaign.projectId)
                        val campaignSetting = CampaignSetting(
                            campaignId = campaign.campaignId,
                            campaignTitle = campaign.title,
//                            imageUrl = campaign.picUrl,
                            imageUrl = sbtImageUrl,
                            projectUrl = project?.projectUrl ?: "",
                            campaignDescription = campaign.description
                        )
                        campaignList.add(campaignSetting)
                    }
                }
            }
            if (sbtIds.isNotEmpty()) {
                for (id in sbtIds) {
                    val sbtSetting = getSbtSettingById(id, userId)
                    if (sbtSetting != null) {
                        sbtList.add(sbtSetting)
                    }
                }
            }
            val banner = Banner(
                title = template.bannerTitle,
                subTitle = template.bannerSubTitle,
                picUrl = template.bannerPicUrl,
                sbtIds = template.bannerSbtIds,
                sbts = sbtList
            )
            val middle = Middle(
                title = template.middleTitle,
                description = template.middleDescription
            )
            val tonHeroCampaignId = template.tonHeroCampaignId
            val tonHeroCampaign = campaignRepo.getCampaignById(tonHeroCampaignId)
            var tonHero: TonHero? = null
            if (tonHeroCampaign != null) {
                val tonHeroTitle = tonHeroCampaign.title
                val tonHeroDescription = tonHeroCampaign.description
                var tonHeroSbtPicUrl = ""
                val tonHeroGroups = credentialGroupService.getCredentialGroupByCampaignId(tonHeroCampaignId)!!
                val sbtReward = sbtRewardRepo.getSBTByGroupId(tonHeroGroups[0].id)
                val tonHeroCredential = tonHeroGroups[0].credentialList[0]
                val options = tonHeroCredential.options
                var requiredSbtAmount = 0
                var userSBTReward: UserSBTList? = null
                var userTonHeroSbtSetting: SbtSetting? = null
                try {
                    val jsonObject = JSONObject(options)
                    requiredSbtAmount = jsonObject.getInt("requiredSbtAmount")
                } catch (e: Exception) {
                    println("get requiredSbtAmount error" + e.message)
                }
                if (sbtReward.isNotEmpty()) {
                    tonHeroSbtPicUrl = sbtReward[0].picUrl
                    userSBTReward =
                        sbtWhiteListRepo.getUserSbtByUidAddressActivityId(
                            userId,
                            tonAddress ?: "",
                            sbtReward[0].activityId
                        )
                    val tonSyncHistory = tonSocietySyncRepository.getTonSyncHistoryBySBTId(sbtReward[0].sbtId)
                    val project = getProject(sbtReward[0].projectId)
                    userTonHeroSbtSetting = SbtSetting(
                        sbtId = sbtReward[0].sbtId,
                        sbtTitle = tonSyncHistory?.sbtCollectionTitle ?: "",
                        sbtActivityId = sbtReward[0].activityId,
                        sbtName = sbtReward[0].name,
                        picUrl = sbtReward[0].picUrl,
                        activityUrl = sbtReward[0].activityUrl,
                        campaignId = tonSyncHistory?.campaignId ?: 0,
                        projectUrl = project?.projectUrl ?: "",
                        claimType = getUserSbtClaimedTypeByUidAddressActivityId(
                            userId,
                            tonAddress ?: "",
                            sbtReward[0].activityId
                        )

                    )
                }
                tonHero = TonHero(
                    campaignId = tonHeroCampaign.campaignId,
                    title = tonHeroTitle,
                    description = tonHeroDescription,
                    sbtPicUrl = tonHeroSbtPicUrl,
                    sbtReward = userTonHeroSbtSetting,
                    sbtNum = requiredSbtAmount,
                    campaignStatus = tonHeroCampaign.status.value,
                    campaignEndAt = tonHeroCampaign.endAt
                )
            }
            val homeProjectTemplateSetting = HomeProjectsTemplateSettings(
                templateName = name,
                banner = banner,
                middle = middle,
                tonHero = tonHero,
                campaignIds = campaignIds,
                campaigns = campaignList
            )
            return homeProjectTemplateSetting
        }
        return null
    }

    fun setHomeProjectsAndCampaignsAndSBTs(homeSettings: HomeProjectsCampaignsSBTsSetting) {
        val banners = homeSettings.banners
        projectRepo.deleteHomeBanner()
        projectRepo.deleteHomeCampaign()
        projectRepo.deleteHomeSbt()
        projectRepo.deleteProject()
        projectRepo.addHomeBanners(banners)
        homeSettings.campaigns.forEach { it.campaignTitle = homeSettings.campaignTitle }
        projectRepo.addHomeCampaigns(homeSettings.campaigns)
        homeSettings.sbts.forEach { it.sbtTitle = homeSettings.sbtTitle }
        projectRepo.addHomeSBTs(homeSettings.sbts)
        homeSettings.projects.forEach { it.projectTitle = homeSettings.projectTitle }
        projectRepo.addHomeProjects(homeSettings.projects)
    }

    fun getHomeProjectsAndCampaignsAndSBTs(): HomeProjectsCampaignsSBTsSetting {
        val banners = projectRepo.getHomeBanners()
        val campaigns = projectRepo.getHomeCampaigns()
        for (campaignSetting in campaigns) {
            val campaignId = campaignSetting.campaignId
            val campaign = campaignRepo.getCampaignById(campaignId)!!
            val project = projectRepo.getProjectById(campaign.projectId)!!
            campaignSetting.projectUrl = project.projectUrl
        }
        val sbts = projectRepo.getHomeSBTs()
        for (sbt in sbts) {
            val sbtId = sbt.sbtId
            val tonSyncSbt = tonSocietySyncRepository.getTonSyncHistoryBySBTId(sbtId)!!
            val activityUrl = tonSyncSbt.activityUrl
            sbt.picUrl = tonSyncSbt.sbtImage
            sbt.activityUrl = activityUrl
            sbt.sbtName = tonSyncSbt.sbtItemTitle
        }
        val projects = projectRepo.getHomeProjects()
        for (projectSetting in projects) {
            val projectId = projectSetting.projectId
            val project = projectRepo.getProjectById(projectId)!!
            projectSetting.projectUrl = project.projectUrl
            projectSetting.projectName = project.projectName
            projectSetting.tags = project.tags
            projectSetting.picUrl = project.avatarUrl
        }
        val homeSettings = HomeProjectsCampaignsSBTsSetting(
            banners = banners,
            campaignTitle = campaigns.firstOrNull()?.campaignTitle ?: "",
            campaigns = campaigns,
            sbtTitle = sbts.firstOrNull()?.sbtTitle ?: "",
            sbts = sbts,
            projectTitle = projects.firstOrNull()?.projectTitle ?: "",
            projects = projects
        )
        return homeSettings
    }

    fun getHomeProjects(): List<Project> {
        var homeProjectIds = emptyList<Long>()
        val homeProjectsCachedData = redisTemplate.opsForValue().get("home-projects")
        if (homeProjectsCachedData != null) {
            homeProjectIds = homeProjectsCachedData.split(",").map { it.toLong() }.toMutableList()
        }
        val homeProjects = projectRepo.getProjectsById(homeProjectIds)
        return homeProjects.sortedBy { homeProjectIds.indexOf(it.projectId) }
    }

    fun getHomeCampaigns(): List<HomeCampaignSetting> {
        val keys = redisTemplate.opsForSet().members("home-campaign-set") ?: return emptyList()

        val dataKeys = keys.map { key -> key.replace("home-campaigns-", "") }
        val campaignData = redisTemplate.opsForValue().multiGet(keys).orEmpty()
        val homeCampaignsData = dataKeys.zip(campaignData).filter { it.second != null }.associate { it }

        val resultCampaignSettings = mutableListOf<HomeCampaignSetting>()
        val allCampaignIds = homeCampaignsData.flatMap { it.value.split(",").map { c -> c.toLong() } }
        val allCampaignsData = campaignRepo.getCampaignsByIds(allCampaignIds).orEmpty().associateBy { it.campaignId }
        val allProjectIds = allCampaignsData.values.map { it.projectId }.distinct()
        val allProjects = projectRepo.getProjectsById(allProjectIds).associateBy { it.projectId }
        val participants = campaignService.getParticipantNumsByCampaignIds(allCampaignIds)

        homeCampaignsData.forEach { (key, value) ->
            val homeCampaignIds = value.split(",").map { it.toLong() }.toMutableList()
            val homeCampaigns = mutableListOf<Campaign>()
            for (campaignId in homeCampaignIds) {
                val campaign = allCampaignsData[campaignId]!!
                val groups = credentialGroupService.getGroupsByCampaignId(campaignId)!!
                val groupIds = groups.map { it.id }
                val points = pointRepo.getPointByGroupIds(groupIds)
                val sbts = sbtRewardRepo.getSBTByGroupIds(groupIds)
                if (points.isNotEmpty() && campaign.points == 0) {
                    campaign.points = 1
                }
                if (sbts.isNotEmpty()) {
                    campaign.hasSBTReward = true
                }
                val project = allProjects[campaign.projectId]!!
                campaign.projectName = project.projectName
                campaign.projectLogoUrl = project.avatarUrl
                campaign.projectUrl = project.projectUrl
                campaign.participantNum = participants[campaignId] ?: 0
                homeCampaigns.add(campaign)
            }
            val homeCampaignSetting = HomeCampaignSetting(
                campaignTitle = key,
                campaignIds = value,
                campaigns = homeCampaigns
            )
            resultCampaignSettings.add(homeCampaignSetting)
        }
        return resultCampaignSettings
    }

    fun updateProject(project: Project): Project? {
        val projectId = project.projectId
        val urlProject = getProjectByUrl(project.projectUrl)
        val nameProject = getProjectByName(project.projectName)
        if (urlProject == null && nameProject == null) {  //url和name做了更改，且不存在与其相同的已有project
            projectRepo.update(project)
            return getProject(projectId)!!
        } else if (urlProject != null && urlProject.projectId == projectId) { //url未做更改
            projectRepo.update(project)
            return getProject(projectId)!!
        } else if (nameProject != null && nameProject.projectId == projectId) { //name未做更改
            projectRepo.update(project)
            return getProject(projectId)!!
        }
        return null
    }

    fun generateKey(projectId: Long): String {
        val projectExternalConfig = projectExternalConfigRepo.getProjectExternalConfig(projectId)
        val bytes = ByteArray(21)
        SecureRandom.getInstanceStrong().nextBytes(bytes)
        val appKey = Base64.getUrlEncoder().encodeToString(bytes)
        if (projectExternalConfig == null) {
            projectExternalConfigRepo.addProjectExternalConfig(projectId, appKey, "", 1, "")
        } else {
            projectExternalConfigRepo.updateAppKey(projectId, appKey)
        }
        return appKey
    }

    fun updateProjectCallbackUrl(projectId: Long, callbackUrl: String) {
        val currentConfig = projectExternalConfigRepo.getProjectExternalConfig(projectId)
        if (currentConfig != null) {
            projectExternalConfigRepo.updateCallbackUrl(projectId, callbackUrl)
        } else {
            projectExternalConfigRepo.addProjectExternalConfig(projectId, "", callbackUrl, 1, "")
        }
    }

    fun updateProjectCallbackStatus(projectId: Long, enable: Boolean) {
        val currentConfig = projectExternalConfigRepo.getProjectExternalConfig(projectId)
        if (currentConfig != null) {
            projectExternalConfigRepo.updateCallbackStatus(projectId, enable)
        } else {
            projectExternalConfigRepo.addProjectExternalConfig(projectId, "", "", if (enable) 1 else 0, "")
        }
    }

    fun updateProjectCallback(projectId: Long, enable: Boolean, callbackUrl: String) {
        val currentConfig = projectExternalConfigRepo.getProjectExternalConfig(projectId)
        if (currentConfig != null) {
            projectExternalConfigRepo.updateCallback(projectId, enable, callbackUrl)
        } else {
            projectExternalConfigRepo.addProjectExternalConfig(projectId, "", callbackUrl, if (enable) 1 else 0, "")
        }
    }

    fun getProjectExternalConfig(projectId: Long): ProjectExternalConfig? {
        return projectExternalConfigRepo.getProjectExternalConfig(projectId)
    }

    fun getProjectExternalConfigByKey(appKey: String): ProjectExternalConfig? {
        return projectExternalConfigRepo.getProjectExternalConfigByAppKey(appKey)
    }

    fun getProjectCreator(projectId: Long): Long? {
        return projectRepo.getProjectCreator(projectId)
    }

    fun getProjectsForCompany(companyId: Long, layerOneId: Long? = null): List<Project> {
        return when {
            (layerOneId == null) -> {
                projectRepo.getProjectListByCompany(companyId)
            }

            else -> {
                projectRepo.getProjectListByCompanyWithLayerOneId(companyId, layerOneId)
            }
        }
    }
}