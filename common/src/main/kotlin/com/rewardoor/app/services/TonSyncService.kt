package com.rewardoor.app.services

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.google.gson.Gson
import com.rewardoor.IdGenerator
import com.rewardoor.app.dao.ProjectRepository
import com.rewardoor.app.dao.SBTRewardRepository
import com.rewardoor.app.dao.SuiSbtRepository
import com.rewardoor.app.dao.TonSocietySyncRepository
import com.rewardoor.model.SuiSbtSync
import com.rewardoor.model.TonSyncHistory
import org.json.JSONObject
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.env.Environment
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.support.TransactionTemplate
import org.springframework.web.client.RestTemplate
import org.springframework.web.reactive.function.client.ExchangeStrategies
import org.springframework.web.reactive.function.client.WebClient
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.util.Base64


@Service
class TonSyncService(
    private val tonSyncRepo: TonSocietySyncRepository,
    private val sbtRewardRepo: SBTRewardRepository,
    private val restTemplate: RestTemplate,
    private val tsTemplate: TransactionTemplate,
    private val mailService: MailService,
    private val env: Environment,
    val projectRepo: ProjectRepository,
    @Value("\${tg.ton-x-partner-id}") private val tonXPartnerId: String,
    @Value("\${tg.ton-x-api-key}") private val tonXApiKey: String,
    @Value("\${ton.sync.mail_receivers}") private val mailReceivers: MutableList<String>,
    private val suiService: SuiService,
    private val idGenerator: IdGenerator,
    private val suiSbtRepository: SuiSbtRepository
) {
    private val mapper = jacksonObjectMapper()

    private final val strategies = ExchangeStrategies.builder()
        .codecs { codecs -> codecs.defaultCodecs().maxInMemorySize(20 * 1024 * 1024) }
        .build()
    private val webClient = WebClient.builder().exchangeStrategies(strategies).build()

    init {
        mapper.registerModules(JavaTimeModule())
    }

    @Transactional
    fun hasPrivilege(campaignId: Long): Boolean {
        return tonSyncRepo.getTonSyncPrivilege(campaignId)
    }

    @Transactional
    fun getSyncHistoryByCampaignId(campaignId: Long): List<TonSyncHistory>? {
        return tonSyncRepo.getTonSyncHistory(campaignId)
    }

    @Transactional
    fun getSyncHistoryByGroupId(groupId: Long): TonSyncHistory? {
        return tonSyncRepo.getTonSyncHistoryByGroupId(groupId)
    }

    @Transactional
    fun getSyncHistoryBySBTId(sbtId: Long): TonSyncHistory? {
        return tonSyncRepo.getTonSyncHistoryBySBTId(sbtId)
    }

    @Transactional
    fun getSyncHistoryByActivityId(activityId: Long): TonSyncHistory? {
        return tonSyncRepo.getTonSyncHistoryByActivityId(activityId)
    }

    @Transactional
    fun addTonSyncHistory(history: TonSyncHistory) {
        tonSyncRepo.addTonSyncHistory(history)
    }

    @Transactional
    fun createSBTCheck(history: TonSyncHistory): TonSyncHistory {
        val existCheck = tonSyncRepo.getTonSyncCheckBySBTId(history.sbtId)
        if (existCheck == null) {
            tonSyncRepo.addTonSyncCheck(history)
        }
        return tonSyncRepo.getTonSyncCheckBySBTId(history.sbtId)!!
    }


    @Transactional
    fun checkSBTStatus(sbtId: Long, checkStatus: Int): Int {
        val updateCnt = tonSyncRepo.updateTonSyncCheck(sbtId, checkStatus)
        if (checkStatus == 1) {
            val req = tonSyncRepo.getTonSyncCheckBySBTId(sbtId)!!
            val projectId = req.projectId
            val projectUrl = projectRepo.getProjectById(projectId)!!.projectUrl
            val data = Data(2, projectUrl, req.campaignId)
//            req.buttonLink = getDirectLink(data, true)
            // pre create 后，改为默认button link
            req.buttonLink = "https://t.me/tbook_incentive_bot/tbook"
            if (tonSyncRepo.getTonSyncHistoryBySBTId(sbtId) == null) {
                if (req.networkId == 0) { // ton
                    syncCampaign(req)
                } else if (req.networkId == 1) {
                    val suiSbtReward = suiSbtRepository.getSuiSbtRewardById(sbtId)!!
                    val suiSbtSync = SuiSbtSync(
                        sbtId = sbtId,
                        projectId = projectId,
                        sbtName = req.sbtItemTitle,
                        sbtDesc = req.sbtDesc,
                        sbtUrl = req.sbtImage,
                        objectId = suiSbtReward.objectId
                    )
                    suiService.syncSuiSbt(suiSbtSync)
                }
            }
        }
        return updateCnt
    }

    @Transactional
    fun getCheckList(): List<TonSyncHistory>? {
        val checkList = tonSyncRepo.getTonSyncCheckList()
        if (checkList != null) {
            for (check in checkList) {
                val project = projectRepo.getProjectById(check.projectId)
                if (project != null) {
                    val projectName = project.projectName
                    check.projectName = projectName
                }
            }
        }
        return checkList?.filter { it.projectName != "" }
    }

    @Transactional
    fun syncCampaign(history: TonSyncHistory): TonSyncHistory {
        val headers = HttpHeaders()
        headers.set("x-partner-id", tonXPartnerId)
        headers.set("x-api-key", tonXApiKey)
        val req = SyncRequest.fromHistoryItem(history)
        val request = HttpEntity(req, headers)
        val p = if (env.activeProfiles.contains("prod")) "PROD" else "STAG"
        //暂时不传 ton society
        if (p == "PROD") {
//            val rawResponse =
//                restTemplate.postForObject("https://id.ton.org/v1/activities", request, String::class.java)
//            println("Raw response: ${rawResponse.toString()}")
            val syncResponse = restTemplate.postForObject(
                "https://id.ton.org/v1/activities", request, SyncResponse::class.java
            )
            val objectMapper = ObjectMapper()
            val jsonResponse = objectMapper.writeValueAsString(syncResponse)
            println("tonSync " + jsonResponse)
            history.activityUrl = syncResponse?.data?.activity_url ?: ""
            history.activityId = syncResponse?.data?.activity_id ?: 0L
        } else {
            history.activityUrl = ""
            history.activityId = 0L
        }
        tsTemplate.execute {
            tonSyncRepo.addTonSyncHistory(history)
        }
        val sbtId = history.sbtId
        val sbtReward = sbtRewardRepo.getSBTById(sbtId)
        if (sbtReward != null && history.activityId != 0L && history.activityUrl != "") {
            sbtRewardRepo.updateSBTActivityId(sbtId, history.activityId.toInt())
            sbtRewardRepo.updateSBTActivityUrl(sbtId, history.activityUrl)
        }
        mailService.sendTonSociety(
            mailReceivers,
            "New TON Society Activity [${p}]",
            "New TON Society Activity: \n\n ${mapper.writeValueAsString(history)}"
        )
        return history
    }

    @Transactional
    fun updateSyncActivity(history: TonSyncHistory): TonSyncHistory {
        val req = SimpleSyncRequest.fromSimpleHistoryItem(history)
        println("update req: $req")
        val p = if (env.activeProfiles.contains("prod")) "PROD" else "STAG"
        //暂时不传 ton society
        if (p == "PROD") {
            val activityId = history.activityId
            val syncResponse = webClient.patch()
                .uri("https://id.ton.org/v1/activities/$activityId")
                .headers { httpHeaders ->
                    httpHeaders.set("x-partner-id", tonXPartnerId)
                    httpHeaders.set("x-api-key", tonXApiKey)
                }
                .bodyValue(req)
                .retrieve()
                .bodyToMono(String::class.java)
                .block()
            val jsonResponse = JSONObject(syncResponse ?: "{}")
            println("update activity " + syncResponse.toString())
//            val data = jsonResponse.getJSONObject("data")
//            history.activityUrl = data.optString("activity_url")
            history.activityId = activityId
            if (jsonResponse.optString("status") == "success") {
                println("update activity $activityId success")
                tonSyncRepo.updateTonSyncHistory(history)
                return tonSyncRepo.getTonSyncHistoryByGroupId(history.groupId)!!
            }
        }
        return history
    }

    @Transactional
    fun updateSbtCategory(sbtId: Long, category: Int) {
        tonSyncRepo.updateCheckSBTCategory(sbtId, category)
        val sbtRewardDetail = sbtRewardRepo.getSBTById(sbtId)
        if (sbtRewardDetail != null) {
            sbtRewardRepo.updateSBTCategory(sbtId, category)
        }
    }

    fun getDirectLink(data: Data, isBot: Boolean): String {
        val tgBotName = if (env.activeProfiles.contains("prod")) "tbook_incentive_bot" else "tbook01_bot"
        val tgBotApp = if (env.activeProfiles.contains("prod")) "tbook" else "tbook"
        val appLink = "https://t.me/$tgBotName"
        val gson = Gson()
        val jsonData = gson.toJson(data)
        val encodedData = Base64.getEncoder().encodeToString(jsonData.toByteArray())
        val link = if (isBot) {
            "$appLink/$tgBotApp?startapp=$encodedData"
        } else {
            appLink
        }
        return link
    }
}

data class Data(val id: Int, val projectUrl: String, val campaignId: Long)

class CtaButton(
    val label: String = "",
    val link: String = ""
)

class CatImage(
    val url: String = ""
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
class CatCollection(
    val title: String = "",
    val description: String = "",
    val image: CatImage = CatImage(),
    val cover: CatImage = CatImage(),
    val metadata: Map<String, Any> = mapOf(),
    val itemTitle: String = "",
    val itemDescription: String = "",
    val itemImage: CatImage = CatImage(),
    val itemVideo: CatImage? = null,
    val itemMetadata: Map<String, Any> = mapOf()
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
class CatReward(
    val mintType: String = "manual",
    val collection: CatCollection = CatCollection()
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
class SyncRequest(
    val title: String = "",
    val subtitle: String = "",
    val description: String = "",
    val hubId: Long = 1,
    val additionalInfo: String = "",
    val startDate: String = "",
    val endDate: String = "",
    val ctaButton: CtaButton = CtaButton(),
    val rewards: CatReward = CatReward()
) {
    companion object {
        fun fromHistoryItem(history: TonSyncHistory): SyncRequest {
            val formatter = DateTimeFormatter
                .ofPattern("yyyy-MM-dd").withZone(ZoneOffset.UTC)
            var sbtVideo: String? = null
            if (history.sbtVideo.endsWith("mp4")) {
                sbtVideo = history.sbtVideo
            }

            val start = history.startDate!!.atOffset(ZoneOffset.UTC).format(formatter)
            val end = history.endDate!!.atOffset(ZoneOffset.UTC).format(formatter)
            return SyncRequest(
                history.title,
                history.subtitle,
                history.description,
                1,
                "",
                start, end,
                CtaButton(history.buttonLabel, history.buttonLink),
                CatReward(
                    "manual",
                    CatCollection(
                        history.sbtCollectionTitle,
                        history.sbtCollectionDesc,
                        CatImage(history.sbtImage),
                        CatImage(history.sbtImage),
                        mapOf("social_links" to listOf(history.buttonLink, "https://t.me/tonsociety")),
                        history.sbtItemTitle,
                        history.sbtDesc,
                        CatImage(history.sbtImage),
                        if (history.sbtVideo.endsWith("mp4")) CatImage(history.sbtVideo) else null,
                        mapOf(
                            "buttons" to listOf(
                                mapOf("uri" to history.buttonLink, "label" to "Open in TON Society")
                            ),
                            "activity_type" to "Event",
                            "organizer" to "Community",
                            "original_activity_url" to history.buttonLink,
                            "start_date_iso" to start,
                            "end_date_iso" to end,
                            "place" to mapOf(
                                "type" to "Online",
                                "country_code_iso" to "",
                                "place_coordinates_osm" to "",
                                "venue_name" to ""
                            ),
                            "user_data" to "",
                            "attributes" to listOf(
                                mapOf("trait_type" to "Strength", "value" to "99")
                            )
                        )
                    )
                )
            )
        }
    }
}

class SimpleSyncRequest(
    val title: String = "",
    val subtitle: String = "",
    val description: String = "",
    val hubId: Long = 1,
    val additionalInfo: String = "",
    val startDate: String = "",
    val endDate: String = "",
    val ctaButton: CtaButton = CtaButton()
//    val rewards: CatReward = CatReward()
) {
    override fun toString(): String {
        return "SimpleSyncRequest(title='$title', subtitle='$subtitle', description='$description', hubId=$hubId, additionalInfo='$additionalInfo', startDate='$startDate', endDate='$endDate', ctaButtonLabel=${ctaButton.label}, ctaButtonLink=${ctaButton.link})"
    }

    companion object {
        fun fromSimpleHistoryItem(history: TonSyncHistory): SimpleSyncRequest {
            val formatter = DateTimeFormatter
                .ofPattern("yyyy-MM-dd").withZone(ZoneOffset.UTC)
            var sbtVideo: String? = null
            if (history.sbtVideo.endsWith("mp4")) {
                sbtVideo = history.sbtVideo
            }

            val start = history.startDate!!.atOffset(ZoneOffset.UTC).format(formatter)
            val end = history.endDate!!.atOffset(ZoneOffset.UTC).format(formatter)
            return SimpleSyncRequest(
                history.title,
                history.subtitle,
                history.description,
                1,
                "",
                start, end,
                CtaButton(history.buttonLabel, history.buttonLink)
            )
        }
    }

}

class SyncData(
    val activity_id: Long = 0,
    val activity_url: String = ""
)

class SyncResponse(
    val status: String = "",
    val message: String = "",
    val data: SyncData = SyncData()
)