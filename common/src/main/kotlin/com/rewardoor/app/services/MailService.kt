package com.rewardoor.app.services

import org.springframework.beans.factory.annotation.Value
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.mail.SimpleMailMessage
import org.springframework.mail.javamail.JavaMailSender
import org.springframework.stereotype.Service
import java.time.Duration
import java.util.UUID


@Service
class MailService(private val emailSender: JavaMailSender,
                  private val redisTemplate: StringRedisTemplate,
                  @Value("\${spring.mail.username}") private val from: String) {

    fun sendMailCaptcha(userId: Long, to: String): String {
        val captcha = (100000..999999).random()

        val subject = "Rewardoor Email Verification"
        val text = "Your verification code is: $captcha"
        sendSimpleMessage(to, subject, text)
        val key = UUID.randomUUID().toString().replace("-", "")
        redisTemplate.opsForValue().set("mail:cap:$userId:$key", captcha.toString(), Duration.ofMinutes(11))
        return key
    }

    fun sendSimpleMessage(to: String, subject: String, text: String) {
        val message = SimpleMailMessage()
        message.from = from
        message.setTo(to)
        message.subject = subject
        message.text = text
        emailSender.send(message)
    }

    fun verifyCaptcha(userId: Long, key: String, captcha: String): Boolean {
        val stored = redisTemplate.opsForValue().get("mail:cap:$userId:$key").toString()
        if (stored == captcha) {
            redisTemplate.delete("mail:cap:$key")
            return true
        }
        return false
    }

    fun sendTonSociety(emails: List<String>, subject: String, text: String) {
        val message = SimpleMailMessage()
        message.from = from
        message.setTo(*emails.toTypedArray())
        message.subject = subject
        message.text = text
        emailSender.send(message)
    }

    fun sendTBook(emails: List<String>, subject: String, text: String) {
        val message = SimpleMailMessage()
        message.from = from
        message.setTo(*emails.toTypedArray())
        message.subject = subject
        message.text = text
        emailSender.send(message)
    }

}